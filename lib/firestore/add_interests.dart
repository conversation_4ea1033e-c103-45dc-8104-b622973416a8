import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:convert';
import 'package:flutter/services.dart' show rootBundle;

Future<void> uploadSAArData() async {
  final FirebaseFirestore firestore = FirebaseFirestore.instance;

  // Load the JSON string (assumes you added sa_ar_interests.json under assets)
  String jsonString =
      await rootBundle.loadString('assets/collections/interests_SA_ar.json');
  Map<String, dynamic> saArData = json.decode(jsonString);

  // Write to Firestore
  await firestore
      .collection('interests')
      .doc('SA_ar')
      .set(saArData, SetOptions(merge: true));
  print("Document 'SA_ar' successfully uploaded to Firestore.");
}
