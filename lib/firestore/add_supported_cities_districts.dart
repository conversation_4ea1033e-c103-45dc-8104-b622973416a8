import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart' show rootBundle;

Future<void> addAllCitiesFromJson() async {
  final firestore = FirebaseFirestore.instance;

  final String response = await rootBundle
      .loadString('assets/collections/supported_cities_riyadh_districts.json');
  final data = json.decode(response);

  final country = data['SA'] as Map<String, dynamic>;

  for (final cityId in country.keys) {
    final cityData = country[cityId];
    final nameAr = cityData['name_ar'];
    final nameEn = cityData['name_en'];
    final districts = cityData['districts'] as Map<String, dynamic>;

    final cityDoc = firestore
        .collection('countries')
        .doc('SA')
        .collection('cities')
        .doc(cityId);

    await cityDoc.set({
      'name_ar': nameAr,
      'name_en': nameEn,
      'districts': {},
    }, SetOptions(merge: true));

    for (final entry in districts.entries) {
      final districtId = entry.key;
      final district = entry.value;
      final location = district['location'];

      await cityDoc.update({
        'districts.$districtId': {
          'name_ar': district['name_ar'],
          'name_en': district['name_en'],
          'location': GeoPoint(location[0], location[1]),
        }
      });
    }
  }

  print('✅ All cities uploaded from JSON');
}
