/// User Model
///
/// Represents a user in the application with all their profile information
/// Used for storing and retrieving user data from Firestore
///
/// This model includes personal information, location, and interests
library user_model;

import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:towasl/shared/models/selected_interest_model.dart';

/// Converts a JSON string to a UserModel object
///
/// @param str JSON string representation of a user
/// @return UserModel instance created from the JSON data
UserModel userModelFromJson(String str) => UserModel.fromJson(json.decode(str));

/// Converts a UserModel object to a JSON string
///
/// @param data UserModel instance to convert
/// @return JSON string representation of the user
String userModelToJson(UserModel data) => json.encode(data.toJson());

/// User Model Class
///
/// Contains all user profile information and preferences
/// All fields are nullable to support partial profile completion
class UserModel {
  /// User's gender (e.g., "male", "female")
  String? gender;

  /// User's nationality
  String? nationality;

  /// Unique user identifier
  String? userId;

  /// User's mobile number (primary identifier)
  String? mobile;

  /// Country code for phone number (e.g., "966" for Saudi Arabia)
  String? countryCode;

  /// User's location information (city, country, coordinates)
  UserLocation? userLocation;

  /// User's birth year (used for age calculation)
  String? birthdayYear;

  /// User's selected interests with category and subcategory IDs
  /// Array of selected interest objects with timestamps
  List<SelectedInterest>? selectedInterests;

  /// Default constructor with optional parameters
  ///
  /// Creates a new UserModel instance with the provided values
  /// All parameters are optional to support partial profile creation
  UserModel(
      {this.gender,
      this.mobile,
      this.nationality,
      this.userId,
      this.userLocation,
      this.birthdayYear,
      this.selectedInterests,
      this.countryCode});

  /// Factory constructor to create a UserModel from JSON
  ///
  /// Parses a JSON map and creates a UserModel instance
  /// Handles null values and type conversions
  ///
  /// @param json Map containing user data from Firestore
  /// @return UserModel instance with data from the JSON map
  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        // Basic user information with empty string fallbacks
        mobile: json["mobile"] ??
            json["email"] ??
            '', // Support both mobile and legacy email field
        gender: json["gender"] ?? '',
        nationality: json["nationality"] ?? '',
        userId:
            json["user_id"] ?? json["id"], // Support both user_id and id fields
        countryCode: json["countryCode"] ?? "",

        // Parse nested UserLocation object if present
        userLocation: json["user_location"] == null
            ? null
            : UserLocation.fromJson(json["user_location"]),

        birthdayYear: json["birthdayYear"] ?? '',

        // Parse selected interests array if present
        // Converts from dynamic types to strongly typed List<SelectedInterest>
        selectedInterests: json["selected_interests"] == null
            ? null
            : List<SelectedInterest>.from(json["selected_interests"]!
                .map((x) => SelectedInterest.fromJson(x))),
      );

  /// Converts the UserModel to a JSON map
  ///
  /// Creates a map representation suitable for storing in Firestore
  ///
  /// @return Map containing all user data in the format expected by Firestore
  Map<String, dynamic> toJson() => {
        // Basic user information
        "mobile": mobile,
        "gender": gender,
        "nationality": nationality,
        "user_id": userId,
        "countryCode": countryCode,

        // Convert nested objects to JSON
        "user_location": userLocation?.toJson(),
        "birthdayYear": birthdayYear,

        // Convert selected interests array to the format expected by Firestore
        "selected_interests":
            selectedInterests?.map((interest) => interest.toJson()).toList(),
      };

  /// Temporary backward compatibility getter for userInterest
  ///
  /// Converts selectedInterests to the old Map<String, List<String>> format
  /// This is for backward compatibility while migrating other parts of the codebase
  ///
  /// @deprecated Use selectedInterests instead
  Map<String, List<String>>? get userInterest {
    if (selectedInterests == null || selectedInterests!.isEmpty) {
      return null;
    }

    final Map<String, List<String>> result = {};

    for (final interest in selectedInterests!) {
      // For backward compatibility, we'll use categoryId as the key
      // and subcategoryId as the value in the list
      if (!result.containsKey(interest.categoryId)) {
        result[interest.categoryId] = [];
      }
      result[interest.categoryId]!.add(interest.subcategoryId);
    }

    return result;
  }
}

/// User Location Class
///
/// Contains geographical information about a user's location
/// Includes both coordinates and address components
class UserLocation {
  /// Country name
  String? country;

  /// Geographic coordinates as GeoPoint
  /// Contains both latitude and longitude in a single field
  GeoPoint? location;

  /// City name
  String? city;

  /// District or neighborhood name
  String? district;

  /// Timestamp when location was last updated
  /// Used for enforcing 30-day restriction on location changes
  DateTime? updatedAt;

  /// Default constructor with optional parameters
  ///
  /// Creates a new UserLocation instance with the provided values
  /// All parameters are optional to support partial location data
  UserLocation({
    this.country,
    this.location,
    this.city,
    this.district,
    this.updatedAt,
  });

  /// Convenience constructor with separate lat/lng parameters
  ///
  /// Creates a UserLocation with coordinates from separate latitude and longitude values
  UserLocation.withCoordinates({
    this.country,
    required double lat,
    required double lng,
    this.city,
    this.district,
    this.updatedAt,
  }) : location = GeoPoint(lat, lng);

  /// Get latitude from GeoPoint
  double? get lat => location?.latitude;

  /// Get longitude from GeoPoint
  double? get lng => location?.longitude;

  /// Factory constructor to create a UserLocation from JSON
  ///
  /// Parses a JSON map and creates a UserLocation instance
  /// Handles type conversions for coordinates and timestamp
  /// Supports both GeoPoint and legacy lat/lng format
  ///
  /// @param json Map containing location data from Firestore
  /// @return UserLocation instance with data from the JSON map
  factory UserLocation.fromJson(Map<String, dynamic> json) {
    GeoPoint? location;

    // Check for GeoPoint field first
    if (json["location"] != null && json["location"] is GeoPoint) {
      location = json["location"] as GeoPoint;
    }
    // Fallback to legacy lat/lng fields for backward compatibility
    else if (json["lat"] != null && json["lng"] != null) {
      final lat = json["lat"]?.toDouble();
      final lng = json["lng"]?.toDouble();
      if (lat != null && lng != null) {
        location = GeoPoint(lat, lng);
      }
    }

    return UserLocation(
      country: json["country"],
      location: location,
      city: json["city"],
      district: json["district"],
      updatedAt: json["updated_at"] != null
          ? (json["updated_at"] is int
              ? DateTime.fromMillisecondsSinceEpoch(json["updated_at"])
              : (json["updated_at"].toDate() as DateTime))
          : null,
    );
  }

  /// Converts the UserLocation to a JSON map
  ///
  /// Creates a map representation suitable for storing in Firestore
  /// Uses GeoPoint for coordinates
  ///
  /// @return Map containing all location data in the format expected by Firestore
  Map<String, dynamic> toJson() => {
        "country": country,
        "location": location,
        "city": city,
        "district": district,
        "updated_at": updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      };

  @override
  String toString() {
    return 'UserLocation(country: $country, city: $city, district: $district, '
        'location: $location, updatedAt: $updatedAt)';
  }
}
