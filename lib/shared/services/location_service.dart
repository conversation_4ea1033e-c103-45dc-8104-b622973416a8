/// Location Service
///
/// Provides methods for handling location-related operations
/// Centralizes location access, permissions, and geocoding
///
/// This service handles location permissions, fetching, and geocoding
library location_service;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:geocoding/geocoding.dart' as geo;
import 'package:geolocator/geolocator.dart';

/// Location data model
///
/// Contains location coordinates and address information
class LocationData {
  /// Geographic coordinates as GeoPoint
  final GeoPoint location;
  final String country;
  final String city;
  final String district;

  LocationData({
    required this.location,
    required this.country,
    required this.city,
    required this.district,
  });

  /// Convenience constructor with separate lat/lng parameters
  LocationData.withCoordinates({
    required double lat,
    required double lng,
    required this.country,
    required this.city,
    required this.district,
  }) : location = GeoPoint(lat, lng);

  /// Get latitude from GeoPoint
  double get lat => location.latitude;

  /// Get longitude from GeoPoint
  double get lng => location.longitude;

  /// Convert to a map for storage or API calls
  Map<String, dynamic> toMap() {
    return {
      'location': location,
      'country': country,
      'city': city,
      'district': district,
    };
  }
}

/// Location Service Interface
///
/// Defines the contract for location operations
/// Allows for easy mocking in tests
abstract class LocationService {
  /// Check if location services are enabled
  ///
  /// @return A Future that resolves to true if enabled, false otherwise
  Future<bool> isLocationServiceEnabled();

  /// Check if location permission is granted
  ///
  /// @return A Future that resolves to true if granted, false otherwise
  Future<bool> isLocationPermissionGranted();

  /// Request location permission
  ///
  /// @return A Future that resolves to true if granted, false otherwise
  Future<bool> requestLocationPermission();

  /// Get the current location
  ///
  /// @return A Future that resolves to a LocationData object
  /// @throws Exception if location services are disabled or permission is denied
  Future<LocationData> getCurrentLocation();

  /// Get a mock location for testing or simulator use
  ///
  /// @return A LocationData object with mock values
  LocationData getMockLocation();
}

/// Location Service Implementation
///
/// Implements the LocationService interface using geolocator and geocoding packages
class LocationServiceImpl implements LocationService {
  /// Constructor
  LocationServiceImpl();

  @override
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await Geolocator.isLocationServiceEnabled();
    } catch (e) {
      if (kDebugMode) {
        print('Error checking location service: $e');
      }
      return false;
    }
  }

  @override
  Future<bool> isLocationPermissionGranted() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      return permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking location permission: $e');
      }
      return false;
    }
  }

  @override
  Future<bool> requestLocationPermission() async {
    try {
      if (kDebugMode) {
        print('LocationService: Requesting location permission');
      }

      // Check if service is enabled first
      bool serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        if (kDebugMode) {
          print('LocationService: Location service not enabled');
        }
        return false;
      }

      // Check current permission
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        // Request permission
        permission = await Geolocator.requestPermission();
      }

      // Check if permission is granted
      bool isGranted = permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse;

      if (kDebugMode) {
        print('LocationService: Permission result: $permission');
        print('LocationService: Permission granted: $isGranted');
      }

      return isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('LocationService: Error requesting permission - $e');
      }
      return false;
    }
  }

  @override
  Future<LocationData> getCurrentLocation() async {
    try {
      if (kDebugMode) {
        print('LocationService: Starting location retrieval');
      }

      // Using real GPS location from device

      // Check if service is enabled
      bool serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      // Check permission
      bool permissionGranted = await isLocationPermissionGranted();
      if (!permissionGranted) {
        if (kDebugMode) {
          print('LocationService: Permission not granted, requesting...');
        }
        permissionGranted = await requestLocationPermission();
        if (!permissionGranted) {
          throw Exception('Location permission denied');
        }
      }

      if (kDebugMode) {
        print('LocationService: Getting current position using geolocator...');
      }

      // Get current position using geolocator
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 30),
      );

      if (kDebugMode) {
        print('LocationService: GPS location obtained successfully');
        print('LocationService: Latitude: ${position.latitude}');
        print('LocationService: Longitude: ${position.longitude}');
        print('LocationService: Starting reverse geocoding...');
      }

      // Set locale for Arabic place names
      geo.setLocaleIdentifier("ar_SA");

      // Convert coordinates to human-readable location with timeout
      List<geo.Placemark> placemarks = await geo
          .placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      )
          .timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          if (kDebugMode) {
            print(
                'LocationService: Geocoding timed out, using coordinates only');
          }
          // Return empty placemarks if geocoding times out
          return <geo.Placemark>[];
        },
      );

      // Extract address components
      String country = '';
      String city = '';
      String district = '';

      if (placemarks.isNotEmpty) {
        country = placemarks.first.country ?? '';
        city = placemarks.first.locality ?? '';
        district = placemarks.first.subLocality ?? '';

        if (kDebugMode) {
          print('LocationService: Reverse geocoding completed');
          print('LocationService: Country: $country');
          print('LocationService: City: $city');
          print('LocationService: District: $district');
        }
      } else {
        if (kDebugMode) {
          print(
              'LocationService: No geocoding results, using coordinates only');
        }
        // Use default values for Saudi Arabia if geocoding fails
        country = 'المملكة العربية السعودية';
        city = 'موقع غير محدد';
        district = 'حي غير محدد';
      }

      return LocationData.withCoordinates(
        lat: position.latitude,
        lng: position.longitude,
        country: country,
        city: city,
        district: district,
      );
    } catch (e) {
      if (kDebugMode) {
        print('LocationService: Error getting location: $e');
      }

      // Always rethrow the error to let the caller handle it
      rethrow;
    }
  }

  @override
  LocationData getMockLocation() {
    return LocationData.withCoordinates(
      lat: 24.680459665769533,
      lng: 46.579983324072145,
      country: "المملكة العربية السعودية",
      city: "الرياض",
      district: "عرقة",
    );
  }
}
