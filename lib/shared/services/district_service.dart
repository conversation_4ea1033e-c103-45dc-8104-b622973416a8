/// District Service
///
/// Service for fetching districts for supported cities from Firestore
/// Works with the new supported_cities document structure with districts
library district_service;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:towasl/shared/services/firebase_service.dart';

/// District Model
///
/// Represents a district with localized names and coordinates
class District {
  /// District key/ID
  final String key;

  /// Arabic name
  final String nameAr;

  /// English name
  final String nameEn;

  /// Geographic coordinates as GeoPoint
  /// Contains both latitude and longitude in a single field
  final GeoPoint? location;

  /// Creates a District
  const District({
    required this.key,
    required this.nameAr,
    required this.nameEn,
    this.location,
  });

  /// Convenience constructor with separate lat/lng parameters
  District.withCoordinates({
    required this.key,
    required this.nameAr,
    required this.nameEn,
    required double latitude,
    required double longitude,
  }) : location = GeoPoint(latitude, longitude);

  /// Get display name based on locale
  String getDisplayName([String locale = 'ar']) {
    return locale == 'ar' ? nameAr : nameEn;
  }

  /// Get latitude from GeoPoint
  double? get latitude => location?.latitude;

  /// Get longitude from GeoPoint
  double? get longitude => location?.longitude;

  /// Check if district has valid coordinates
  bool get hasValidCoordinates =>
      location != null &&
      location!.latitude != 0.0 &&
      location!.longitude != 0.0;

  /// Get coordinates as a formatted string
  String get coordinatesString => hasValidCoordinates
      ? '${location!.latitude}, ${location!.longitude}'
      : '';

  @override
  String toString() {
    return 'District(key: $key, nameAr: $nameAr, nameEn: $nameEn, location: $location)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is District &&
        other.key == key &&
        other.nameAr == nameAr &&
        other.nameEn == nameEn &&
        other.location == location;
  }

  @override
  int get hashCode =>
      key.hashCode ^ nameAr.hashCode ^ nameEn.hashCode ^ location.hashCode;
}

/// District Service Interface
///
/// Defines the contract for district operations
abstract class DistrictService {
  /// Get districts for a specific city
  ///
  /// @param cityKey The city key to get districts for
  /// @return A Future that resolves to a list of districts
  Future<List<District>> getDistrictsForCity(String cityKey);

  /// Check if a city has districts available
  ///
  /// @param cityKey The city key to check
  /// @return A Future that resolves to true if city has districts
  Future<bool> hasCityDistricts(String cityKey);

  /// Find a district by name within a city
  ///
  /// @param cityKey The city key to search in
  /// @param districtName The district name to search for (Arabic or English)
  /// @return A Future that resolves to the matching district or null
  Future<District?> findDistrictByName(String cityKey, String districtName);
}

/// District Service Implementation
///
/// Implements district fetching using Firebase Firestore
/// Works with app_settings/supported_cities document structure
class DistrictServiceImpl implements DistrictService {
  /// Firebase service for Firestore operations
  final FirebaseService _firebaseService;

  /// Collection name in Firestore
  static const String _collectionName = 'app_settings';

  /// Document ID for supported cities
  static const String _documentId = 'supported_cities';

  /// Field name for Saudi Arabia cities map
  static const String _citiesField = 'SA';

  /// Field name for districts map
  static const String _districtsField = 'districts';

  /// Constructor with dependency injection
  DistrictServiceImpl({required FirebaseService firebaseService})
      : _firebaseService = firebaseService;

  @override
  Future<List<District>> getDistrictsForCity(String cityKey) async {
    try {
      if (cityKey.isEmpty) {
        if (kDebugMode) {
          print('DistrictService: Empty city key provided');
        }
        return [];
      }

      if (kDebugMode) {
        print('DistrictService: Fetching districts for city: $cityKey');
      }

      final doc = await _firebaseService.getDocument(
        _collectionName,
        _documentId,
      );

      if (!doc.exists || doc.data() == null) {
        if (kDebugMode) {
          print('DistrictService: Supported cities document not found');
        }
        return [];
      }

      final data = doc.data() as Map<String, dynamic>;

      if (!data.containsKey(_citiesField)) {
        if (kDebugMode) {
          print('DistrictService: SA field not found in document');
        }
        return [];
      }

      final citiesData = data[_citiesField];
      if (citiesData is! Map<String, dynamic>) {
        if (kDebugMode) {
          print('DistrictService: SA data is not a map');
        }
        return [];
      }

      // Find the specific city
      if (!citiesData.containsKey(cityKey)) {
        if (kDebugMode) {
          print('DistrictService: City key "$cityKey" not found');
        }
        return [];
      }

      final cityData = citiesData[cityKey];
      if (cityData is! Map<String, dynamic>) {
        if (kDebugMode) {
          print('DistrictService: City data is not a map');
        }
        return [];
      }

      // Check if city has districts
      if (!cityData.containsKey(_districtsField)) {
        if (kDebugMode) {
          print('DistrictService: No districts field found for city: $cityKey');
        }
        return [];
      }

      final districtsData = cityData[_districtsField];
      if (districtsData is! Map<String, dynamic>) {
        if (kDebugMode) {
          print('DistrictService: Districts data is not a map');
        }
        return [];
      }

      // Parse districts
      final districts = <District>[];
      for (final entry in districtsData.entries) {
        final districtKey = entry.key;
        final districtData = entry.value;

        if (districtData is Map<String, dynamic>) {
          final nameAr = districtData['name_ar']?.toString() ?? '';
          final nameEn = districtData['name_en']?.toString() ?? '';

          GeoPoint? location;

          // Check for GeoPoint field first
          if (districtData.containsKey('location') &&
              districtData['location'] is GeoPoint) {
            location = districtData['location'] as GeoPoint;
          }
          // Fallback to legacy lat/lng fields for backward compatibility
          else {
            double? latitude;
            double? longitude;

            if (districtData.containsKey('lat')) {
              final latValue = districtData['lat'];
              if (latValue != null) {
                if (latValue is num) {
                  latitude = latValue.toDouble();
                } else if (latValue is String) {
                  latitude = double.tryParse(latValue);
                }
              }
            }

            if (districtData.containsKey('lng')) {
              final lngValue = districtData['lng'];
              if (lngValue != null) {
                if (lngValue is num) {
                  longitude = lngValue.toDouble();
                } else if (lngValue is String) {
                  longitude = double.tryParse(lngValue);
                }
              }
            }

            // Create GeoPoint from lat/lng if both are available
            if (latitude != null && longitude != null) {
              location = GeoPoint(latitude, longitude);
            }
          }

          if (nameAr.isNotEmpty || nameEn.isNotEmpty) {
            districts.add(District(
              key: districtKey,
              nameAr: nameAr,
              nameEn: nameEn,
              location: location,
            ));
          }
        }
      }

      if (kDebugMode) {
        print(
            'DistrictService: Found ${districts.length} districts for city: $cityKey');
      }

      return districts;
    } catch (e) {
      if (kDebugMode) {
        print(
            'DistrictService: Error fetching districts for city "$cityKey" - $e');
      }
      return [];
    }
  }

  @override
  Future<bool> hasCityDistricts(String cityKey) async {
    try {
      final districts = await getDistrictsForCity(cityKey);
      return districts.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('DistrictService: Error checking if city has districts - $e');
      }
      return false;
    }
  }

  @override
  Future<District?> findDistrictByName(
      String cityKey, String districtName) async {
    try {
      if (cityKey.isEmpty || districtName.isEmpty) {
        if (kDebugMode) {
          print('DistrictService: Empty city key or district name provided');
        }
        return null;
      }

      if (kDebugMode) {
        print(
            'DistrictService: Finding district "$districtName" in city: $cityKey');
      }

      final districts = await getDistrictsForCity(cityKey);

      // Search for exact match first (case-insensitive)
      final exactMatches = districts
          .where((district) =>
              district.nameAr.toLowerCase() == districtName.toLowerCase() ||
              district.nameEn.toLowerCase() == districtName.toLowerCase())
          .toList();

      if (exactMatches.isNotEmpty) {
        final exactMatch = exactMatches.first;
        if (kDebugMode) {
          print('DistrictService: Found exact match: ${exactMatch.nameAr}');
        }
        return exactMatch;
      }

      // Search for partial match if no exact match found
      final partialMatches = districts
          .where((district) =>
              district.nameAr
                  .toLowerCase()
                  .contains(districtName.toLowerCase()) ||
              district.nameEn
                  .toLowerCase()
                  .contains(districtName.toLowerCase()))
          .toList();

      if (partialMatches.isNotEmpty) {
        final partialMatch = partialMatches.first;
        if (kDebugMode) {
          print('DistrictService: Found partial match: ${partialMatch.nameAr}');
        }
        return partialMatch;
      }

      if (kDebugMode) {
        print('DistrictService: No district found matching "$districtName"');
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('DistrictService: Error finding district "$districtName" - $e');
      }
      return null;
    }
  }
}
